// Pentesting Slides Script

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const slideContainer = document.getElementById('slideContainer');
    const slideIndicators = document.getElementById('slideIndicators');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const currentSlideEl = document.getElementById('currentSlide');
    const totalSlidesEl = document.getElementById('totalSlides');
    const progressBar = document.getElementById('progressBar');
    const categoryFilter = document.getElementById('categoryFilter');
    const searchInput = document.getElementById('searchInput');
    const tocBtn = document.getElementById('tocBtn');
    const tocModal = document.getElementById('tocModal');
    const closeTocBtn = document.getElementById('closeTocBtn');
    const tocBody = document.getElementById('tocBody');
    
    // State variables
    let slides = [];
    let currentSlideIndex = 0;
    let filteredSlides = [];
    
    // Terminal typing effect
    function setupTerminalEffect() {
        const terminalLines = [
            "nmap -sS -sV -O target.com",
            "dirb http://target.com /usr/share/wordlists/dirb/common.txt",
            "sqlmap -u \"http://target.com/page.php?id=1\" --dbs",
            "hydra -l admin -P /usr/share/wordlists/rockyou.txt target.com http-post-form",
            "wpscan --url http://target.com --enumerate u",
            "gobuster dir -u http://target.com -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt"
        ];
        
        const terminalBody = document.querySelector('.terminal-body');
        const promptSpan = document.querySelector('.prompt');
        const commandSpan = document.querySelector('.command');
        const outputDiv = document.querySelector('.terminal-output');
        
        let currentLineIndex = 0;
        
        function typeNextCommand() {
            if (currentLineIndex >= terminalLines.length) {
                currentLineIndex = 0;
            }
            
            const command = terminalLines[currentLineIndex];
            commandSpan.textContent = '';
            
            let charIndex = 0;
            const typeInterval = setInterval(() => {
                if (charIndex < command.length) {
                    commandSpan.textContent += command.charAt(charIndex);
                    charIndex++;
                } else {
                    clearInterval(typeInterval);
                    setTimeout(showOutput, 500);
                }
            }, 50);
        }
        
        function showOutput() {
            // Different outputs based on the command
            let outputs = [];
            
            switch (currentLineIndex) {
                case 0: // nmap
                    outputs = [
                        "Starting Nmap 7.94 ( https://nmap.org )",
                        "PORT     STATE SERVICE VERSION",
                        "22/tcp   open  ssh     OpenSSH 8.2p1",
                        "80/tcp   open  http    Apache httpd 2.4.41",
                        "443/tcp  open  https   Apache httpd 2.4.41"
                    ];
                    break;
                case 1: // dirb
                    outputs = [
                        "DIRB v2.22",
                        "START_TIME: Wed Jun 5 10:15:22 2024",
                        "FOUND: http://target.com/admin/",
                        "FOUND: http://target.com/login/",
                        "FOUND: http://target.com/backup/"
                    ];
                    break;
                case 2: // sqlmap
                    outputs = [
                        "sqlmap identified the following injection point(s)",
                        "Parameter: id (GET)",
                        "Type: boolean-based blind",
                        "Title: AND boolean-based blind - WHERE or HAVING clause",
                        "available databases [5]:",
                        "- information_schema",
                        "- mysql",
                        "- performance_schema",
                        "- sys",
                        "- target_db"
                    ];
                    break;
                case 3: // hydra
                    outputs = [
                        "Hydra v9.4 (c) 2022 by van Hauser/THC",
                        "Hydra (https://github.com/vanhauser-thc/thc-hydra)",
                        "[DATA] max 16 tasks per 1 server",
                        "[DATA] attacking http-post-form://target.com:80/login.php",
                        "[80][http-post-form] host: target.com   login: admin   password: admin123"
                    ];
                    break;
                case 4: // wpscan
                    outputs = [
                        "WordPress version 5.8.3 identified",
                        "Vulnerable plugins:",
                        "contact-form-7 version 5.4.1",
                        "Users identified:",
                        "admin (Administrator)",
                        "editor (Editor)"
                    ];
                    break;
                case 5: // gobuster
                    outputs = [
                        "Gobuster v3.1.0",
                        "/images              (Status: 301) [Size: 313] [-->http://target.com/images/]",
                        "/admin               (Status: 301) [Size: 312] [-->http://target.com/admin/]",
                        "/js                  (Status: 301) [Size: 309] [-->http://target.com/js/]",
                        "/css                 (Status: 301) [Size: 310] [-->http://target.com/css/]"
                    ];
                    break;
            }
            
            // Clear previous output
            outputDiv.innerHTML = '';
            
            // Add new output lines
            outputs.forEach(line => {
                const div = document.createElement('div');
                div.textContent = line;
                outputDiv.appendChild(div);
            });
            
            // Add typing cursor
            const cursor = document.createElement('div');
            cursor.className = 'typing-cursor';
            cursor.textContent = '|';
            outputDiv.appendChild(cursor);
            
            currentLineIndex++;
            setTimeout(typeNextCommand, 3000);
        }
        
        // Start the terminal effect
        typeNextCommand();
    }
    
    // Fetch slides data
    async function fetchSlides() {
        try {
            const response = await fetch('web_pentesting.json');
            if (!response.ok) {
                throw new Error('Failed to fetch slides data');
            }
            
            const rawData = await response.json();

            // Validate and sanitize JSON data
            slides = validateAndSanitizeSlides(rawData);
            filteredSlides = [...slides];
            
            // Update total slides count
            totalSlidesEl.textContent = slides.length;
            
            // Create slides and indicators
            createSlides();
            createIndicators();
            
            // Show first slide
            showSlide(0);
            
            // Setup terminal effect
            setupTerminalEffect();
        } catch (error) {
            console.error('Error fetching slides:', error);
            // Create error message safely
            slideContainer.innerHTML = '';
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = 'Failed to load slides. Please try again later.';
            slideContainer.appendChild(errorDiv);
        }
    }
    
    // HTML escape function to prevent XSS
    function escapeHtml(text) {
        if (typeof text !== 'string') {
            return '';
        }
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Sanitize input to prevent XSS in search
    function sanitizeInput(input) {
        if (typeof input !== 'string') {
            return '';
        }
        // Remove any HTML tags, script tags, and dangerous characters
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<[^>]*>/g, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .replace(/data:/gi, '')
            .substring(0, 100);
    }

    // Validate and sanitize slide data
    function validateAndSanitizeSlides(data) {
        if (!Array.isArray(data)) {
            console.error('Invalid slide data: not an array');
            return [];
        }

        return data.map(slide => {
            // Ensure all required properties exist
            const sanitizedSlide = {
                id: typeof slide.id === 'number' ? slide.id : 0,
                title: typeof slide.title === 'string' ? sanitizeInput(slide.title) : 'Untitled Slide',
                category: ['basics', 'intermediate', 'advanced'].includes(slide.category) ? slide.category : 'basics',
                content: typeof slide.content === 'string' ? sanitizeInput(slide.content) : '',
                codeExample: typeof slide.codeExample === 'string' ? slide.codeExample : '',
                image: typeof slide.image === 'string' ? slide.image : 'assets_I/images/placeholder-terminal.png'
            };

            // Handle tools array if present
            if (slide.tools && Array.isArray(slide.tools)) {
                sanitizedSlide.tools = slide.tools.map(tool =>
                    typeof tool === 'string' ? sanitizeInput(tool) : ''
                ).filter(tool => tool !== '');
            }

            // Handle references array if present
            if (slide.references && Array.isArray(slide.references)) {
                sanitizedSlide.references = slide.references.map(reference =>
                    typeof reference === 'string' ? sanitizeInput(reference) : ''
                ).filter(reference => reference !== '');
            }

            // Handle keyPoints based on format
            if (Array.isArray(slide.keyPoints)) {
                sanitizedSlide.keyPoints = slide.keyPoints.map(point => {
                    if (typeof point === 'string') {
                        return sanitizeInput(point);
                    } else if (typeof point === 'object' && point !== null) {
                        return {
                            title: typeof point.title === 'string' ? sanitizeInput(point.title) : '',
                            description: typeof point.description === 'string' ? sanitizeInput(point.description) : '',
                            example: typeof point.example === 'string' ? sanitizeInput(point.example) : ''
                        };
                    } else {
                        return '';
                    }
                });
            } else {
                sanitizedSlide.keyPoints = ['No key points available'];
            }

            return sanitizedSlide;
        });
    }

    // Create slides from data with XSS protection
    function createSlides() {
        slideContainer.innerHTML = '';

        filteredSlides.forEach((slide, index) => {
            const slideElement = document.createElement('div');
            slideElement.className = 'slide';
            slideElement.dataset.index = index;

            // Create slide content container
            const slideContent = document.createElement('div');
            slideContent.className = 'slide-content';

            // Create category badge
            const categorySpan = document.createElement('span');
            categorySpan.className = `slide-category ${escapeHtml(slide.category)}`;
            categorySpan.textContent = slide.category;
            slideContent.appendChild(categorySpan);

            // Create title
            const titleH2 = document.createElement('h2');
            titleH2.className = 'slide-title';
            titleH2.textContent = slide.title;
            slideContent.appendChild(titleH2);

            // Create description
            const descriptionP = document.createElement('p');
            descriptionP.className = 'slide-description';
            descriptionP.textContent = slide.content;
            slideContent.appendChild(descriptionP);

            // Create key points section
            const keyPointsDiv = document.createElement('div');
            keyPointsDiv.className = 'key-points';

            const keyPointsTitle = document.createElement('h3');
            keyPointsTitle.textContent = 'Key Points';
            keyPointsDiv.appendChild(keyPointsTitle);

            const keyPointsList = document.createElement('div');
            keyPointsList.className = 'key-points-list';

            slide.keyPoints.forEach(point => {
                const keyPointItem = document.createElement('div');
                keyPointItem.className = 'key-point-item';

                if (typeof point === 'string') {
                    const keyPointTitle = document.createElement('div');
                    keyPointTitle.className = 'key-point-title';
                    keyPointTitle.textContent = point;
                    keyPointItem.appendChild(keyPointTitle);
                } else {
                    const keyPointTitle = document.createElement('div');
                    keyPointTitle.className = 'key-point-title';
                    keyPointTitle.textContent = point.title;
                    keyPointItem.appendChild(keyPointTitle);

                    const keyPointDescription = document.createElement('div');
                    keyPointDescription.className = 'key-point-description';
                    keyPointDescription.textContent = point.description;
                    keyPointItem.appendChild(keyPointDescription);

                    const keyPointExample = document.createElement('div');
                    keyPointExample.className = 'key-point-example';

                    const exampleStrong = document.createElement('strong');
                    exampleStrong.textContent = 'Example: ';
                    keyPointExample.appendChild(exampleStrong);

                    const exampleText = document.createTextNode(point.example);
                    keyPointExample.appendChild(exampleText);

                    keyPointItem.appendChild(keyPointExample);
                }

                keyPointsList.appendChild(keyPointItem);
            });

            keyPointsDiv.appendChild(keyPointsList);
            slideContent.appendChild(keyPointsDiv);

            // Create code example
            const codeExample = document.createElement('pre');
            codeExample.className = 'code-example';
            codeExample.textContent = slide.codeExample;
            slideContent.appendChild(codeExample);

            // Add tools section if available
            if (slide.tools && Array.isArray(slide.tools) && slide.tools.length > 0) {
                const toolsSection = document.createElement('div');
                toolsSection.className = 'tools-section';

                const toolsTitle = document.createElement('h4');
                toolsTitle.textContent = 'Recommended Tools';
                toolsSection.appendChild(toolsTitle);

                const toolsList = document.createElement('div');
                toolsList.className = 'tools-list';

                slide.tools.forEach(tool => {
                    const toolBadge = document.createElement('span');
                    toolBadge.className = 'tool-badge';
                    toolBadge.textContent = sanitizeInput(tool);
                    toolsList.appendChild(toolBadge);
                });

                toolsSection.appendChild(toolsList);
                slideContent.appendChild(toolsSection);
            }

            // Add references section if available
            if (slide.references && Array.isArray(slide.references) && slide.references.length > 0) {
                const referencesSection = document.createElement('div');
                referencesSection.className = 'references-section';

                const referencesTitle = document.createElement('h4');
                referencesTitle.textContent = 'References';
                referencesSection.appendChild(referencesTitle);

                const referencesList = document.createElement('div');
                referencesList.className = 'references-list';

                slide.references.forEach(reference => {
                    const referenceBadge = document.createElement('span');
                    referenceBadge.className = 'reference-badge';
                    referenceBadge.textContent = sanitizeInput(reference);
                    referencesList.appendChild(referenceBadge);
                });

                referencesSection.appendChild(referencesList);
                slideContent.appendChild(referencesSection);
            }

            // Create slide image container
            const slideImageDiv = document.createElement('div');
            slideImageDiv.className = 'slide-image';

            const slideImg = document.createElement('img');
            slideImg.src = slide.image;
            slideImg.alt = slide.title;
            slideImg.onerror = function() {
                this.src = 'assets_I/images/placeholder-terminal.png';
            };
            slideImageDiv.appendChild(slideImg);

            // Append content and image to slide
            slideElement.appendChild(slideContent);
            slideElement.appendChild(slideImageDiv);

            slideContainer.appendChild(slideElement);
        });
    }
    
    // Create slide indicators
    function createIndicators() {
        slideIndicators.innerHTML = '';
        
        filteredSlides.forEach((_, index) => {
            const indicator = document.createElement('div');
            indicator.className = 'indicator';
            indicator.dataset.index = index;
            
            indicator.addEventListener('click', () => {
                showSlide(index);
            });
            
            slideIndicators.appendChild(indicator);
        });
    }
    
    // Show slide by index
    function showSlide(index) {
        if (filteredSlides.length === 0) return;
        
        // Ensure index is within bounds
        if (index < 0) index = 0;
        if (index >= filteredSlides.length) index = filteredSlides.length - 1;
        
        currentSlideIndex = index;
        
        // Update current slide display
        currentSlideEl.textContent = index + 1;
        
        // Update slides
        const slideElements = document.querySelectorAll('.slide');
        slideElements.forEach((slide, i) => {
            if (i === index) {
                slide.classList.add('active');
            } else {
                slide.classList.remove('active');
            }
        });
        
        // Update indicators
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach((indicator, i) => {
            if (i === index) {
                indicator.classList.add('active');
            } else {
                indicator.classList.remove('active');
            }
        });
        
        // Update progress bar
        const progress = ((index + 1) / filteredSlides.length) * 100;
        progressBar.style.width = `${progress}%`;
    }

    // Create table of contents
    function createTableOfContents() {
        const tocList = document.createElement('ul');
        tocList.className = 'toc-list';

        slides.forEach((slide, index) => {
            const tocItem = document.createElement('li');
            tocItem.className = 'toc-item';

            const tocLink = document.createElement('a');
            tocLink.className = 'toc-link';
            tocLink.href = '#';
            tocLink.dataset.slideIndex = index;

            // Create TOC elements safely
            const tocNumber = document.createElement('span');
            tocNumber.className = 'toc-number';
            tocNumber.textContent = slide.id;

            const tocTitle = document.createElement('span');
            tocTitle.className = 'toc-title';
            tocTitle.textContent = slide.title;

            const tocCategory = document.createElement('span');
            tocCategory.className = `toc-category ${escapeHtml(slide.category)}`;
            tocCategory.textContent = slide.category;

            tocLink.appendChild(tocNumber);
            tocLink.appendChild(tocTitle);
            tocLink.appendChild(tocCategory);

            tocLink.addEventListener('click', (e) => {
                e.preventDefault();
                showSlide(index);
                closeToc();
            });

            tocItem.appendChild(tocLink);
            tocList.appendChild(tocItem);
        });

        tocBody.innerHTML = '';
        tocBody.appendChild(tocList);
    }

    // Show table of contents
    function showToc() {
        createTableOfContents();
        tocModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // Close table of contents
    function closeToc() {
        tocModal.classList.remove('active');
        document.body.style.overflow = '';
    }
    
    // Filter slides by category
    function filterSlides() {
        // Sanitize inputs to prevent XSS
        const category = sanitizeInput(categoryFilter.value);
        const searchTerm = sanitizeInput(searchInput.value).toLowerCase();

        if (category === 'all' && searchTerm === '') {
            filteredSlides = [...slides];
        } else {
            filteredSlides = slides.filter(slide => {
                const categoryMatch = category === 'all' || slide.category === category;

                // Enhanced search functionality
                let searchMatch = searchTerm === '';
                if (searchTerm !== '') {
                    searchMatch = slide.title.toLowerCase().includes(searchTerm) ||
                        slide.content.toLowerCase().includes(searchTerm) ||
                        slide.codeExample.toLowerCase().includes(searchTerm);

                    // Search in keyPoints (handle both old and new format)
                    if (!searchMatch) {
                        searchMatch = slide.keyPoints.some(point => {
                            if (typeof point === 'string') {
                                return point.toLowerCase().includes(searchTerm);
                            } else {
                                return point.title.toLowerCase().includes(searchTerm) ||
                                       point.description.toLowerCase().includes(searchTerm) ||
                                       point.example.toLowerCase().includes(searchTerm);
                            }
                        });
                    }
                }

                return categoryMatch && searchMatch;
            });
        }
        
        // Recreate slides and indicators
        createSlides();
        createIndicators();
        
        // Update total count
        totalSlidesEl.textContent = filteredSlides.length;
        
        // Show first slide or no results message
        if (filteredSlides.length > 0) {
            showSlide(0);
        } else {
            // Create no results message safely
            slideContainer.innerHTML = '';
            const noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'no-results';
            noResultsDiv.textContent = 'No slides match your search criteria.';
            slideContainer.appendChild(noResultsDiv);
            slideIndicators.innerHTML = '';
            currentSlideEl.textContent = '0';
            progressBar.style.width = '0%';
        }
    }
    
    // Event listeners
    prevBtn.addEventListener('click', () => {
        showSlide(currentSlideIndex - 1);
    });
    
    nextBtn.addEventListener('click', () => {
        showSlide(currentSlideIndex + 1);
    });
    
    categoryFilter.addEventListener('change', filterSlides);
    
    searchInput.addEventListener('input', function(e) {
        // Sanitize input in real-time
        const sanitized = sanitizeInput(e.target.value);
        if (e.target.value !== sanitized) {
            e.target.value = sanitized;
        }
        filterSlides();
    });
    
    // Keyboard navigation
    document.addEventListener('keydown', (e) => {
        // Only handle keyboard navigation if not typing in search box
        if (document.activeElement !== searchInput) {
            switch(e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    showSlide(currentSlideIndex - 1);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    showSlide(currentSlideIndex + 1);
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(filteredSlides.length - 1);
                    break;
                case 'Escape':
                    e.preventDefault();
                    searchInput.value = '';
                    categoryFilter.value = 'all';
                    filterSlides();
                    break;
            }
        }
    });

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    slideContainer.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
    });

    slideContainer.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // Swipe left - next slide
                showSlide(currentSlideIndex + 1);
            } else {
                // Swipe right - previous slide
                showSlide(currentSlideIndex - 1);
            }
        }
    }

    // Auto-play functionality (optional)
    let autoPlayInterval;
    let isAutoPlaying = false;

    function startAutoPlay() {
        if (isAutoPlaying) return;

        isAutoPlaying = true;
        autoPlayInterval = setInterval(() => {
            if (currentSlideIndex >= filteredSlides.length - 1) {
                showSlide(0);
            } else {
                showSlide(currentSlideIndex + 1);
            }
        }, 10000); // 10 seconds per slide
    }

    function stopAutoPlay() {
        if (!isAutoPlaying) return;

        isAutoPlaying = false;
        clearInterval(autoPlayInterval);
    }

    // Pause auto-play on user interaction
    [prevBtn, nextBtn, slideContainer].forEach(element => {
        element.addEventListener('click', stopAutoPlay);
    });

    // Update button states
    function updateButtonStates() {
        prevBtn.disabled = currentSlideIndex === 0;
        nextBtn.disabled = currentSlideIndex === filteredSlides.length - 1;
    }

    // Enhanced showSlide function
    const originalShowSlide = showSlide;
    showSlide = function(index) {
        originalShowSlide(index);
        updateButtonStates();
    };

    // Add loading state
    function showLoading() {
        slideContainer.innerHTML = '';
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading';
        slideContainer.appendChild(loadingDiv);
    }

    // Initialize with loading state
    showLoading();
    fetchSlides();
});
