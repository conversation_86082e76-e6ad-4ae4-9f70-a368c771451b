/* Pentesting Page Specific Styles */

:root {
    /* Cybersecurity-themed color palette */
    --dark-bg: #0a0e17;
    --darker-bg: #060a12;
    --terminal-bg: #0c0c16;
    --accent-green: #00ff8c;
    --accent-blue: #0084ff;
    --accent-red: #ff3860;
    --accent-yellow: #ffdd57;
    --text-primary: #e6e6e6;
    --text-secondary: #a0a0a0;
    --code-bg: #1a1a2e;
    --card-bg: rgba(26, 27, 38, 0.8);
    --card-border: rgba(66, 66, 102, 0.5);
    --slide-shadow: rgba(0, 0, 0, 0.5);
}

body {
    background-color: var(--dark-bg);
    color: var(--text-primary);
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.pentest-hero {
    background: linear-gradient(135deg, var(--dark-bg), var(--darker-bg));
    padding: 80px 0 40px;
    position: relative;
    overflow: hidden;
}

.pentest-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('assets_I/images/cyber-grid.png');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin-bottom: 40px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--text-primary);
    text-shadow: 0 0 10px rgba(0, 132, 255, 0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.hero-stats {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-green);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Terminal Effect */
.hero-terminal {
    background-color: var(--terminal-bg);
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.terminal-header {
    background-color: #2d2d3a;
    padding: 10px 15px;
    display: flex;
    align-items: center;
}

.terminal-buttons {
    display: flex;
    gap: 8px;
    margin-right: 15px;
}

.btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.btn.red { background-color: #ff5f56; }
.btn.yellow { background-color: #ffbd2e; }
.btn.green { background-color: #27c93f; }

.terminal-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-family: 'Fira Code', monospace;
}

.terminal-body {
    padding: 15px;
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    height: 200px;
    overflow-y: auto;
}

.terminal-line {
    margin-bottom: 10px;
}

.prompt {
    color: var(--accent-green);
    margin-right: 10px;
}

.command {
    color: var(--text-primary);
}

.terminal-output {
    color: var(--text-secondary);
    margin-left: 20px;
    line-height: 1.4;
}

.typing-cursor {
    display: inline-block;
    width: 8px;
    height: 15px;
    background-color: var(--accent-green);
    animation: blink 1s step-end infinite;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
}

/* Slide Controls */
.slide-controls {
    background-color: var(--darker-bg);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.controls-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.slide-navigation {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background-color: var(--accent-blue);
    color: white;
}

.slide-counter {
    font-family: 'Fira Code', monospace;
    color: var(--text-secondary);
}

.filter-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-select {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    background-color: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--card-border);
    padding: 8px 15px 8px 35px;
    border-radius: 4px;
    width: 200px;
}

.search-box ion-icon {
    position: absolute;
    left: 10px;
    color: var(--text-secondary);
}

/* Slides Section */
.slides-section {
    padding: 40px 0;
}

.slides-wrapper {
    position: relative;
}

.slide-container {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 10px 30px var(--slide-shadow);
    overflow: hidden;
    min-height: 500px;
}

/* Individual Slide Styles */
.slide {
    display: none;
    padding: 30px;
    animation: fadeEffect 0.5s;
}

.slide.active {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

@keyframes fadeEffect {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-content {
    display: flex;
    flex-direction: column;
}

.slide-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--accent-blue);
}

.slide-category {
    display: inline-block;
    background-color: var(--accent-blue);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 15px;
    text-transform: uppercase;
}

.slide-category.basics { background-color: var(--accent-green); }
.slide-category.intermediate { background-color: var(--accent-blue); }
.slide-category.advanced { background-color: var(--accent-red); }

.slide-description {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.key-points {
    margin-bottom: 20px;
}

.key-points h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--text-primary);
}

.key-points ul {
    list-style-type: none;
    padding-left: 0;
}

.key-points li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 8px;
}

.key-points li:before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--accent-blue);
}

.code-example {
    background-color: var(--code-bg);
    border-radius: 6px;
    padding: 15px;
    font-family: 'Fira Code', monospace;
    font-size: 0.9rem;
    overflow-x: auto;
    white-space: pre;
    color: var(--text-primary);
    border-left: 3px solid var(--accent-blue);
}

.slide-image {
    display: flex;
    align-items: center;
    justify-content: center;
}

.slide-image img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Slide Indicators */
.slide-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--card-border);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background-color: var(--accent-blue);
    transform: scale(1.2);
}

/* Progress Bar */
.progress-container {
    width: 100%;
    height: 4px;
    background-color: var(--card-border);
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
}

.progress-bar {
    height: 100%;
    background-color: var(--accent-green);
    width: 0;
    transition: width 0.3s ease;
}

/* Error and No Results Messages */
.error-message, .no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.error-message {
    color: var(--accent-red);
}

/* Loading Animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading::after {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid var(--card-border);
    border-top: 4px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Code Syntax Highlighting */
.code-example {
    position: relative;
}

.code-example::before {
    content: 'Terminal';
    position: absolute;
    top: -25px;
    left: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
    background-color: var(--darker-bg);
    padding: 2px 8px;
    border-radius: 4px 4px 0 0;
}

/* Slide Transitions */
.slide {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease;
}

.slide.active {
    opacity: 1;
    transform: translateX(0);
}

/* Enhanced Button Styles */
.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-btn:disabled:hover {
    background-color: var(--card-bg);
    color: var(--text-primary);
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--darker-bg);
    color: var(--text-primary);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .hero-content {
        max-width: 100%;
    }

    .pentest-hero {
        padding: 60px 0 30px;
    }
}

@media (max-width: 992px) {
    .slide.active {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .controls-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .slide-navigation {
        justify-content: space-between;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }

    .search-box input {
        width: 100%;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .terminal-body {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .hero-stats {
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }

    .slide-indicators {
        max-height: 100px;
        overflow-y: auto;
    }

    .filter-controls {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .filter-select {
        flex: 1;
        min-width: 120px;
    }

    .search-box {
        flex: 2;
        min-width: 200px;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 15px;
    }

    .slide-title {
        font-size: 1.5rem;
    }

    .slide {
        padding: 20px;
    }

    .nav-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .terminal-body {
        height: 120px;
        font-size: 0.8rem;
    }

    .code-example {
        font-size: 0.8rem;
        padding: 10px;
    }

    .filter-controls {
        flex-direction: column;
    }

    .filter-select, .search-box {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .pentest-hero {
        padding: 40px 0 20px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .slide-controls {
        padding: 10px 0;
    }

    .slide {
        padding: 15px;
    }

    .slide-title {
        font-size: 1.3rem;
    }

    .stat-number {
        font-size: 2rem;
    }
}
