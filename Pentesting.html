<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive Web Penetration Testing Guide - Tools, Techniques, and Methodologies">
    <meta name="keywords" content="penetration testing, web security, cybersecurity, ethical hacking, OWASP">

    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://i.postimg.cc; connect-src 'self';">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">

    <title>Web Penetration Testing Guide | Cyber Wolf</title>
    
    <!-- Fonts and Icons -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600&display=swap" rel="stylesheet">
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="pentesting-styles.css">
    <link rel="icon" href="https://i.postimg.cc/PxTnhmmb/favicon-1.png">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <a href="index.html" class="logo">
                <div class="logo-container">
                    <img src="assets_I/logo/logo.jpeg" alt="Cyber Wolf Logo" class="logo-image">
                    <span class="logo-text">Cyber Wolf</span>
                </div>
            </a>
            
            <nav class="navbar">
                <ul class="navbar-list">
                    <li><a href="index.html" class="navbar-link">Home</a></li>
                    <li><a href="blog.html" class="navbar-link">Blog</a></li>
                    <li><a href="#" class="navbar-link active">Pentesting</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section class="pentest-hero">
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">Web Penetration Testing Mastery</h1>
                    <p class="hero-subtitle">Comprehensive guide covering 35+ advanced penetration testing topics with tools, techniques, and real-world examples</p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-number">35+</span>
                            <span class="stat-label">Topics</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">150+</span>
                            <span class="stat-label">Tools & Commands</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3</span>
                            <span class="stat-label">Skill Levels</span>
                        </div>
                    </div>
                </div>
                <div class="hero-terminal">
                    <div class="terminal-header">
                        <div class="terminal-buttons">
                            <span class="btn red"></span>
                            <span class="btn yellow"></span>
                            <span class="btn green"></span>
                        </div>
                        <span class="terminal-title">cyber-wolf@pentest:~$</span>
                    </div>
                    <div class="terminal-body">
                        <div class="terminal-line">
                            <span class="prompt">root@kali:~#</span>
                            <span class="command">nmap -sS -sV -O target.com</span>
                        </div>
                        <div class="terminal-output">
                            <div>Starting Nmap 7.94 ( https://nmap.org )</div>
                            <div>PORT     STATE SERVICE VERSION</div>
                            <div>22/tcp   open  ssh     OpenSSH 8.2p1</div>
                            <div>80/tcp   open  http    Apache httpd 2.4.41</div>
                            <div>443/tcp  open  https   Apache httpd 2.4.41</div>
                            <div class="typing-cursor">|</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Controls -->
        <section class="slide-controls">
            <div class="container">
                <div class="controls-wrapper">
                    <div class="slide-navigation">
                        <button class="nav-btn prev-btn" id="prevBtn">
                            <ion-icon name="chevron-back-outline"></ion-icon>
                            Previous
                        </button>
                        <div class="slide-counter">
                            <span id="currentSlide">1</span> / <span id="totalSlides">35</span>
                        </div>
                        <button class="nav-btn next-btn" id="nextBtn">
                            Next
                            <ion-icon name="chevron-forward-outline"></ion-icon>
                        </button>
                    </div>

                    <div class="filter-controls">
                        <button class="nav-btn" id="tocBtn">
                            <ion-icon name="list-outline"></ion-icon>
                            Table of Contents
                        </button>

                        <select id="categoryFilter" class="filter-select">
                            <option value="all">All Categories</option>
                            <option value="basics">Basics</option>
                            <option value="intermediate">Intermediate</option>
                            <option value="advanced">Advanced</option>
                        </select>

                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="Search topics...">
                            <ion-icon name="search-outline"></ion-icon>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Table of Contents Modal -->
        <div class="toc-modal" id="tocModal">
            <div class="toc-content">
                <div class="toc-header">
                    <h3>Table of Contents</h3>
                    <button class="close-btn" id="closeTocBtn">
                        <ion-icon name="close-outline"></ion-icon>
                    </button>
                </div>
                <div class="toc-body" id="tocBody">
                    <!-- Table of contents will be generated here -->
                </div>
            </div>
        </div>

        <!-- Course Overview Table -->
        <section class="course-overview">
            <div class="container">
                <h2 class="overview-title">Web Penetration Testing Course Overview</h2>
                <div class="overview-table-container">
                    <table class="overview-table">
                        <thead>
                            <tr>
                                <th>Category</th>
                                <th>Topics Covered</th>
                                <th>Key Skills</th>
                                <th>Tools & Techniques</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="category-cell basics">
                                    <span class="category-badge basics">Basics</span>
                                    <div class="slide-count">12 Slides</div>
                                </td>
                                <td>
                                    <ul>
                                        <li>Web Penetration Testing Introduction</li>
                                        <li>Testing Methodology & Framework</li>
                                        <li>Web Reconnaissance Techniques</li>
                                        <li>Legal & Ethical Considerations</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Understanding web security fundamentals</li>
                                        <li>Passive information gathering</li>
                                        <li>Technology fingerprinting</li>
                                        <li>Scope definition and planning</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Nmap, WhatWeb, Sublist3r</li>
                                        <li>Google Dorking, Wayback Machine</li>
                                        <li>OWASP ZAP, Burp Suite</li>
                                        <li>Wappalyzer, Retire.js</li>
                                    </ul>
                                </td>
                            </tr>
                            <tr>
                                <td class="category-cell intermediate">
                                    <span class="category-badge intermediate">Intermediate</span>
                                    <div class="slide-count">15 Slides</div>
                                </td>
                                <td>
                                    <ul>
                                        <li>Web Application Scanning</li>
                                        <li>Vulnerability Assessment</li>
                                        <li>OWASP Top 10 Testing</li>
                                        <li>Authentication & Session Testing</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Automated vulnerability scanning</li>
                                        <li>Manual verification techniques</li>
                                        <li>SQL injection & XSS testing</li>
                                        <li>API security assessment</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>SQLmap, Nikto, Ffuf</li>
                                        <li>Hydra, Wfuzz, Arachni</li>
                                        <li>Burp Suite Professional</li>
                                        <li>OWASP ZAP, w3af</li>
                                    </ul>
                                </td>
                            </tr>
                            <tr>
                                <td class="category-cell advanced">
                                    <span class="category-badge advanced">Advanced</span>
                                    <div class="slide-count">8 Slides</div>
                                </td>
                                <td>
                                    <ul>
                                        <li>Advanced Exploitation Techniques</li>
                                        <li>WAF Bypass Methods</li>
                                        <li>Client-Side Security Testing</li>
                                        <li>Business Logic Flaws</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Complex vulnerability chaining</li>
                                        <li>Custom payload development</li>
                                        <li>Advanced evasion techniques</li>
                                        <li>Report writing & remediation</li>
                                    </ul>
                                </td>
                                <td>
                                    <ul>
                                        <li>Custom scripts & exploits</li>
                                        <li>Advanced Burp extensions</li>
                                        <li>Manual testing techniques</li>
                                        <li>Professional reporting tools</li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Slides Container -->
        <section class="slides-section">
            <div class="container">
                <div class="slides-wrapper">
                    <div class="slide-container" id="slideContainer">
                        <!-- Slides will be dynamically generated here -->
                    </div>

                    <!-- Slide Indicators -->
                    <div class="slide-indicators" id="slideIndicators">
                        <!-- Indicators will be dynamically generated here -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Bar -->
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <img src="assets_I/logo/logo.jpeg" alt="Cyber Wolf Logo" class="footer-logo">
                    <p>Advanced cybersecurity education and training platform</p>
                </div>
                <div class="footer-links">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="blog.html">Blog</a></li>
                        <li><a href="#top">Back to Top</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Cyber Wolf. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="pentesting-script.js"></script>
</body>
</html>
